# Project Documentation Summary

Generated from: `test_docs`
Extraction mode: full_context
Estimated tokens: ~1,329
Estimated reading time: 1 minutes
Total files: 6, Total snippets: 23

---

## File: filtering_test.md

### Filtering Test Document

```python
def calculate_total(items):
    """Calculate total price of items."""
    total = 0
    for item in items:
        if item.price > 0:
            total += item.price
    return total

# API call example
response = requests.get('/api/users')
users = response.json()
```

### API call example

```html
<div class="container">
    <h1>Welcome</h1>
    <p>This is just static content.</p>
    <div class="footer">
        <span>Copyright 2024</span>
    </div>
</div>
```

### API call example

```css
.container {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background-color: #f0f0f0;
    border-bottom: 1px solid #ccc;
}
```

### API call example

```javascript
function fetchUserData(userId) {
    return fetch(`/api/users/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            return data;
        });
}

// Event handling
document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('submit');
    button.onclick = handleSubmit;
});
```

### API call example

```html
<form onsubmit="handleSubmit(event)">
    <input type="text" ng-model="user.name" required>
    <button type="submit" :disabled="!isValid">Submit</button>
    <div *ngIf="showError" class="error">{{ errorMessage }}</div>
</form>
```

### API call example

```css
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
}

@media (max-width: 768px) {
    .container {
        padding: var(--primary-color);
        display: none;
    }
}

.dynamic-content {
    color: var(--primary-color);
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
```

### API call example

```javascript
function fetchUserData(userId) {

```inline
/api/users/${userId}
```

        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            return data;
        });
}


## File: sample.md

### Sample Documentation

Here's a simple Python function:

```python
def hello_world():
    """Print hello world."""
    print("Hello, World!")
    return True
```

### Sample Documentation

And here's some JavaScript:

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
    return name;
}
```

### Sample Documentation

```javascript
function greet(name) {

```inline
Hello, ${name}!
```

    return name;
}
```

### Sample Documentation

```inline
inline code
```


## File: test.adoc

Here's a Python code block:

```python
def hello_world():
    """Print hello world."""
    print("Hello, World!")
    return True
```

== JavaScript Example

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
    return name;
}
```

Some shell commands:

```
$ pip install generate-llms
$ generate-llms --help
```

[source,javascript]
----
function greet(name) {

```inline
Hello, ${name}!
```

    return name;
}


## File: test.html

### HTML Code Examples

    <p>Here's some JavaScript:</p>

```javascript

function greet(name) {
    console.log(`Hello, ${name}!`);
    return name;
}
```

    <p>And some Python:</p>
    <code>print("Hello World")</code>

### HTML Code Examples

    <p>And some Python:</p>

```python
print("Hello World")
```

    <script>
    // Inline script
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded');
    });
    </script>
</body>
</html>

### HTML Code Examples

    <p>And some Python:</p>
    <code>print("Hello World")</code>

```javascript
document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded');
    });
```

</body>
</html>

### HTML Code Examples

    <p>Here's some JavaScript:</p>
    <pre><code class="language-javascript">
function greet(name) {

```inline
Hello, ${name}!
```

    return name;
}
    </code></pre>


## File: test.ipynb

### Test Jupyter Notebook

This is a test notebook with code and markdown cells.

```python
# Python code cell
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
```

### Python code cell

Let's do some data analysis:

```python
import pandas as pd
import numpy as np

# Create sample data
data = pd.DataFrame({
    'x': np.random.randn(100),
    'y': np.random.randn(100)
})

print(data.head())
```


## File: test.rst

### Test RST Document

--------------

```python
def hello_world():
    """Print hello world."""
    print("Hello, World!")
    return True
```

### Test RST Document

    $ pip install generate-llms
    $ generate-llms --help

```bash
echo "Hello from bash"
ls -la
```


---

## Processing Metadata

```json
{
  "generation": {
    "timestamp": "2025-08-16T04:05:12.432298",
    "tool_version": "1.0.0",
    "processing_mode": "full_context",
    "llm_rewrite_enabled": false
  },
  "source": {
    "root_path": "C:\\Users\\<USER>\\Desktop\\tie-fighter\\test_docs",
    "total_files_scanned": 6,
    "files_with_snippets": 0,
    "excluded_files": 0,
    "scan_depth": null
  },
  "content_metrics": {
    "total_snippets": 0,
    "snippets_by_language": {},
    "estimated_tokens": 0,
    "estimated_reading_time_minutes": 0
  },
  "quality_indicators": {
    "trust_score": 0.0,
    "completeness_score": 0.0,
    "code_coverage_percentage": 0.0
  },
  "processing": {
    "processing_time_seconds": 0.0,
    "errors": [],
    "warnings": []
  }
}
```
