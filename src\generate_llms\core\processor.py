"""
Main document processor orchestrating the entire pipeline.
"""

import time
import logging
import gc
from pathlib import Path
from typing import List, Iterator, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

# Optional performance monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

from .models import ProcessingConfig, ProcessingResult, ParsedContent
from .config import load_config, merge_config_with_cli
from ..scanners import RecursiveFileScanner
from ..parsers import ContentParserFactory
from ..extractors import RegexCodeExtractor, ContextExtractor
from ..llm import LLMBackendFactory
from ..writers import OutputWriter, MarkdownOutputWriter, MetadataGenerator
from ..utils import TokenCounter, TextChunker


class DocumentProcessor:
    """
    Main processor that orchestrates the entire document processing pipeline.
    """
    
    def __init__(self, config: ProcessingConfig):
        """
        Initialize the document processor.

        Args:
            config: Processing configuration
        """
        self.config = config
        self._setup_logging()

        # Performance monitoring
        self.start_time = None
        self.memory_limit_mb = getattr(config, 'memory_limit_mb', 1024)  # Default 1GB
        self.max_workers = getattr(config, 'max_workers', 4)
        self.batch_size = getattr(config, 'batch_size', 100)

        # Load and merge configuration file if provided
        if config.config_file:
            try:
                file_config = load_config(config.config_file)
                self.config = merge_config_with_cli(file_config, config)
                logging.info(f"Loaded configuration from {config.config_file}")
            except Exception as e:
                logging.warning(f"Failed to load config file: {e}")
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        level = logging.DEBUG if self.config.verbose else logging.INFO
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        if PSUTIL_AVAILABLE:
            try:
                process = psutil.Process()
                return process.memory_info().rss / 1024 / 1024
            except Exception:
                return 0.0
        else:
            # Fallback if psutil not available
            return 0.0

    def _check_memory_usage(self) -> None:
        """Check memory usage and warn if approaching limits."""
        current_memory = self._get_memory_usage_mb()

        if current_memory > self.memory_limit_mb * 0.8:  # 80% threshold
            logging.warning(f"High memory usage: {current_memory:.1f}MB (limit: {self.memory_limit_mb}MB)")

            if current_memory > self.memory_limit_mb:
                logging.error(f"Memory limit exceeded: {current_memory:.1f}MB > {self.memory_limit_mb}MB")
                # Force garbage collection
                gc.collect()

                # Check again after GC
                new_memory = self._get_memory_usage_mb()
                logging.info(f"Memory after garbage collection: {new_memory:.1f}MB")

    def _process_files_in_batches(self, files: List[Path]) -> Iterator[List[ParsedContent]]:
        """Process files in batches to manage memory usage."""
        for i in range(0, len(files), self.batch_size):
            batch = files[i:i + self.batch_size]
            logging.debug(f"Processing batch {i // self.batch_size + 1}: {len(batch)} files")

            # Check memory before processing batch
            self._check_memory_usage()

            # Parse files in current batch
            parsed_batch = []
            parser_factory = ContentParserFactory()

            for file_path in batch:
                try:
                    parser = parser_factory.get_parser(file_path)
                    parsed_content = parser.parse(file_path, self.config)
                    parsed_batch.append(parsed_content)

                except Exception as e:
                    logging.error(f"Failed to parse {file_path}: {e}")
                    # Create error content
                    error_content = ParsedContent(
                        file_path=file_path,
                        file_type="unknown",
                        encoding="utf-8",
                        raw_content="",
                        snippets=[],
                        error=str(e)
                    )
                    parsed_batch.append(error_content)

            yield parsed_batch

            # Force garbage collection after each batch
            gc.collect()

    def _parse_files_parallel(self, files: List[Path]) -> List[ParsedContent]:
        """Parse files in parallel using ThreadPoolExecutor."""
        parsed_contents = []
        parser_factory = ContentParserFactory()

        def parse_single_file(file_path: Path) -> ParsedContent:
            """Parse a single file (thread-safe)."""
            try:
                parser = parser_factory.get_parser(file_path)
                return parser.parse(file_path, self.config)
            except Exception as e:
                logging.error(f"Failed to parse {file_path}: {e}")
                return ParsedContent(
                    file_path=file_path,
                    file_type="unknown",
                    encoding="utf-8",
                    raw_content="",
                    snippets=[],
                    error=str(e)
                )

        # Use ThreadPoolExecutor for I/O-bound operations
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all parsing tasks
            future_to_file = {
                executor.submit(parse_single_file, file_path): file_path
                for file_path in files
            }

            # Collect results as they complete
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    parsed_content = future.result()
                    parsed_contents.append(parsed_content)

                    if len(parsed_contents) % 50 == 0:  # Progress logging
                        logging.debug(f"Parsed {len(parsed_contents)}/{len(files)} files")
                        self._check_memory_usage()

                except Exception as e:
                    logging.error(f"Parallel parsing failed for {file_path}: {e}")
                    # Create error content
                    error_content = ParsedContent(
                        file_path=file_path,
                        file_type="unknown",
                        encoding="utf-8",
                        raw_content="",
                        snippets=[],
                        error=str(e)
                    )
                    parsed_contents.append(error_content)

        return parsed_contents

    def _should_use_parallel_processing(self, file_count: int) -> bool:
        """Determine if parallel processing should be used."""
        # Use parallel processing for larger datasets
        return file_count > 20 and self.max_workers > 1
    
    def process(self) -> ProcessingResult:
        """
        Process documents according to configuration.
        
        Returns:
            ProcessingResult with metrics and metadata
        """
        start_time = time.time()
        logging.info(f"Starting document processing: {self.config.input_directory}")
        
        # Initialize result
        result = ProcessingResult(
            total_files_scanned=0,
            files_with_snippets=0,
            excluded_files=0,
            total_snippets=0,
            scan_depth=self.config.max_depth,
        )
        
        try:
            # Step 1: Scan files (placeholder - will be implemented in next task)
            logging.info("Scanning files...")
            scanned_files = self._scan_files()
            result.total_files_scanned = len(scanned_files)
            
            # Step 2: Parse content (placeholder - will be implemented in next task)
            logging.info("Parsing content...")
            parsed_contents = self._parse_files(scanned_files)
            
            # Step 3: Extract code snippets (placeholder - will be implemented in next task)
            logging.info("Extracting code snippets...")
            self._extract_snippets(parsed_contents)
            
            # Step 4: Apply LLM enhancement if enabled
            if self.config.llm_rewrite_enabled or self.config.llm_backend:
                logging.info("Applying LLM enhancement...")
                self._enhance_content(parsed_contents)
            
            # Step 5: Generate output (placeholder - will be implemented in next task)
            if not self.config.dry_run:
                logging.info("Generating output...")
                self._generate_output(parsed_contents, result)
            
            # Calculate final metrics
            self._calculate_metrics(parsed_contents, result)
            
        except Exception as e:
            logging.error(f"Processing failed: {e}")
            result.errors.append(str(e))
            raise
        
        finally:
            result.processing_time_seconds = time.time() - start_time
            logging.info(f"Processing completed in {result.processing_time_seconds:.2f} seconds")
        
        return result
    
    def _scan_files(self) -> List[Path]:
        """Scan directory for supported files."""
        logging.info("Scanning files with RecursiveFileScanner")

        scanner = RecursiveFileScanner()
        scan_result = scanner.scan(self.config.input_directory, self.config)

        if scan_result.errors:
            for error in scan_result.errors:
                logging.error(f"Scan error: {error}")

        logging.info(f"Found {len(scan_result.files)} files, excluded {scan_result.excluded_count}")
        return scan_result.files
    
    def _parse_files(self, files: List[Path]) -> List[ParsedContent]:
        """Parse files with automatic performance optimization."""
        if self._should_use_parallel_processing(len(files)):
            logging.info(f"Using parallel processing with {self.max_workers} workers")
            return self._parse_files_parallel(files)
        else:
            logging.info("Using sequential processing")
            return self._parse_files_sequential(files)

    def _parse_files_sequential(self, files: List[Path]) -> List[ParsedContent]:
        """Parse files sequentially with memory management."""
        logging.info(f"Parsing {len(files)} files sequentially")

        # Use batch processing for large file sets
        if len(files) > self.batch_size:
            logging.info(f"Using batch processing with batch size: {self.batch_size}")
            all_parsed_contents = []

            for batch in self._process_files_in_batches(files):
                all_parsed_contents.extend(batch)

                # Log progress
                logging.info(f"Processed {len(all_parsed_contents)}/{len(files)} files")

            return all_parsed_contents

        # Standard sequential processing for smaller file sets
        parser_factory = ContentParserFactory()
        parsed_contents = []

        for i, file_path in enumerate(files):
            try:
                parsed_content = parser_factory.parse_file(file_path, self.config)
                if parsed_content:
                    parsed_contents.append(parsed_content)
                    if parsed_content.error:
                        logging.warning(f"Parse error for {file_path}: {parsed_content.error}")
                else:
                    logging.warning(f"No parser available for {file_path}")

                # Periodic memory checks
                if i % 50 == 0 and i > 0:
                    self._check_memory_usage()

            except Exception as e:
                logging.error(f"Failed to parse {file_path}: {e}")

        logging.info(f"Successfully parsed {len(parsed_contents)} files")
        return parsed_contents
    
    def _extract_snippets(self, contents: List[ParsedContent]) -> None:
        """Extract code snippets from parsed content and add context."""
        logging.info(f"Extracting code snippets from {len(contents)} files")

        extractor = RegexCodeExtractor()
        context_extractor = ContextExtractor()
        total_snippets = 0

        for content in contents:
            if content.error:
                logging.warning(f"Skipping extraction for {content.file_path} due to parse error")
                continue

            try:
                # Extract code snippets
                extraction_result = extractor.extract(content, self.config)

                # Update the content with extracted snippets
                content.snippets = extraction_result.snippets
                total_snippets += extraction_result.total_extracted

                # Add context to snippets based on processing mode
                if content.snippets:
                    context_extractor.add_context_to_snippets(content, self.config)
                    logging.debug(f"Added context to {len(content.snippets)} snippets from {content.file_path}")

                if extraction_result.errors:
                    for error in extraction_result.errors:
                        logging.error(f"Extraction error: {error}")

                logging.debug(f"Extracted {extraction_result.total_extracted} snippets from {content.file_path}")

            except Exception as e:
                logging.error(f"Failed to extract snippets from {content.file_path}: {e}")

        logging.info(f"Total snippets extracted: {total_snippets}")
    
    def _enhance_content(self, contents: List[ParsedContent]) -> None:
        """Apply LLM enhancement to content."""
        if not self.config.llm_backend:
            logging.info("LLM enhancement disabled (no backend specified)")
            return

        logging.info(f"Enhancing content with {self.config.llm_backend} backend")

        # Create LLM backend
        factory = LLMBackendFactory()
        backend = factory.create_backend(self.config.llm_backend)

        if not backend:
            logging.warning(f"LLM backend '{self.config.llm_backend}' not available, skipping enhancement")
            return

        if not backend.is_available():
            logging.warning(f"LLM backend '{self.config.llm_backend}' not available, skipping enhancement")
            return

        # Initialize chunking utilities if enabled
        token_counter = None
        text_chunker = None
        if self.config.enable_chunking:
            token_counter = TokenCounter(self.config.llm_model)
            text_chunker = TextChunker(token_counter)
            logging.info(f"Chunking enabled: max {self.config.max_chunk_tokens} tokens per chunk")

        total_snippets = 0
        enhanced_snippets = 0
        failed_enhancements = 0

        for content in contents:
            if not content.snippets:
                continue

            logging.debug(f"Enhancing {len(content.snippets)} snippets from {content.file_path}")

            for snippet in content.snippets:
                total_snippets += 1

                try:
                    # Build context for enhancement
                    context_parts = []
                    if snippet.heading:
                        context_parts.append(f"Section: {snippet.heading}")
                    if snippet.context_before:
                        context_parts.append(f"Context: {snippet.context_before}")

                    context = " | ".join(context_parts) if context_parts else "No additional context"

                    # Check if chunking is needed
                    if self.config.enable_chunking and token_counter and text_chunker:
                        snippet_tokens = token_counter.count_tokens(snippet.content)

                        if snippet_tokens > self.config.max_chunk_tokens:
                            # Chunk the content and process each chunk
                            enhanced_content = self._enhance_chunked_content(
                                snippet, context, backend, text_chunker
                            )

                            if enhanced_content:
                                snippet.original_content = snippet.content
                                snippet.content = enhanced_content
                                enhanced_snippets += 1
                                logging.debug(f"Enhanced chunked snippet from {content.file_path} (line {snippet.start_line})")
                            else:
                                failed_enhancements += 1
                                logging.warning(f"Failed to enhance chunked snippet from {content.file_path}")
                        else:
                            # Process normally (no chunking needed)
                            response = backend.enhance_content(
                                snippet.content,
                                context,
                                self.config
                            )

                            if response.success:
                                snippet.original_content = snippet.content
                                snippet.content = response.enhanced_content
                                enhanced_snippets += 1
                                logging.debug(f"Enhanced snippet from {content.file_path} (line {snippet.start_line})")
                            else:
                                failed_enhancements += 1
                                logging.warning(f"Failed to enhance snippet: {response.error}")
                    else:
                        # Process normally (chunking disabled)
                        response = backend.enhance_content(
                            snippet.content,
                            context,
                            self.config
                        )

                        if response.success:
                            snippet.original_content = snippet.content
                            snippet.content = response.enhanced_content
                            enhanced_snippets += 1
                            logging.debug(f"Enhanced snippet from {content.file_path} (line {snippet.start_line})")
                        else:
                            failed_enhancements += 1
                            logging.warning(f"Failed to enhance snippet: {response.error}")

                except Exception as e:
                    failed_enhancements += 1
                    logging.error(f"Enhancement error for snippet in {content.file_path}: {e}")

        logging.info(f"Enhancement complete: {enhanced_snippets}/{total_snippets} snippets enhanced, {failed_enhancements} failed")

    def _enhance_chunked_content(self, snippet, context: str, backend, text_chunker) -> str:
        """
        Enhance content that needs to be chunked due to size.

        Args:
            snippet: Code snippet to enhance
            context: Context information for the snippet
            backend: LLM backend to use for enhancement
            text_chunker: TextChunker instance for splitting content

        Returns:
            Enhanced content or None if enhancement failed
        """
        try:
            # Split content into chunks
            chunks = text_chunker.chunk_text(
                snippet.content,
                self.config.max_chunk_tokens,
                self.config.chunk_overlap_tokens
            )

            if not chunks:
                logging.warning("No chunks created for large snippet")
                return None

            logging.info(f"Processing snippet in {len(chunks)} chunks")

            enhanced_chunks = []

            for chunk in chunks:
                try:
                    # Add chunk information to context
                    chunk_context = f"{context} | Chunk {chunk.chunk_index + 1}/{chunk.total_chunks}"
                    if chunk.overlap_with_previous:
                        chunk_context += " (overlaps with previous)"
                    if chunk.overlap_with_next:
                        chunk_context += " (overlaps with next)"

                    # Enhance the chunk
                    response = backend.enhance_content(
                        chunk.content,
                        chunk_context,
                        self.config
                    )

                    if response.success:
                        enhanced_chunks.append(response.enhanced_content)
                        logging.debug(f"Enhanced chunk {chunk.chunk_index + 1}/{chunk.total_chunks}")
                    else:
                        logging.warning(f"Failed to enhance chunk {chunk.chunk_index + 1}: {response.error}")
                        # Use original content for failed chunks
                        enhanced_chunks.append(chunk.content)

                except Exception as e:
                    logging.error(f"Error enhancing chunk {chunk.chunk_index + 1}: {e}")
                    # Use original content for failed chunks
                    enhanced_chunks.append(chunk.content)

            # Combine enhanced chunks
            if enhanced_chunks:
                # Join chunks with appropriate separators
                combined_content = self._combine_enhanced_chunks(enhanced_chunks, chunks)
                return combined_content
            else:
                logging.warning("No enhanced chunks to combine")
                return None

        except Exception as e:
            logging.error(f"Error in chunked content enhancement: {e}")
            return None

    def _combine_enhanced_chunks(self, enhanced_chunks: List[str], original_chunks) -> str:
        """
        Combine enhanced chunks into a single coherent document.

        Args:
            enhanced_chunks: List of enhanced chunk contents
            original_chunks: Original chunk objects with metadata

        Returns:
            Combined enhanced content
        """
        if not enhanced_chunks:
            return ""

        if len(enhanced_chunks) == 1:
            return enhanced_chunks[0]

        # For multiple chunks, add separators and chunk indicators
        combined_parts = []

        for i, (enhanced_chunk, original_chunk) in enumerate(zip(enhanced_chunks, original_chunks)):
            # Add chunk header for multi-chunk content
            if len(enhanced_chunks) > 1:
                chunk_header = f"\n<!-- Chunk {i + 1}/{len(enhanced_chunks)} -->\n"
                combined_parts.append(chunk_header)

            combined_parts.append(enhanced_chunk.strip())

            # Add separator between chunks (except for the last one)
            if i < len(enhanced_chunks) - 1:
                combined_parts.append("\n\n")

        return "".join(combined_parts)

    def _generate_output(self, contents: List[ParsedContent], result: ProcessingResult) -> None:
        """Generate final output file."""
        logging.info(f"Generating output file: {self.config.output_file}")

        # Create output writer
        writer = MarkdownOutputWriter()

        try:
            # Generate output and get metrics
            output_metrics = writer.write(contents, result, self.config)

            # Update result with output metrics
            result.estimated_tokens = output_metrics.estimated_tokens
            result.estimated_reading_time_minutes = output_metrics.estimated_reading_time_minutes

            # Log output statistics
            logging.info(f"Output generated successfully:")
            logging.info(f"  - File: {self.config.output_file}")
            logging.info(f"  - Estimated tokens: {output_metrics.estimated_tokens:,}")
            logging.info(f"  - Estimated reading time: {output_metrics.estimated_reading_time_minutes} minutes")
            logging.info(f"  - Total snippets: {output_metrics.total_snippets}")

            # Log context window warnings if any
            if output_metrics.context_window_warnings:
                logging.warning("Context window warnings:")
                for warning in output_metrics.context_window_warnings:
                    logging.warning(f"  - {warning}")

        except Exception as e:
            error_msg = f"Failed to generate output: {e}"
            logging.error(error_msg)
            result.errors.append(error_msg)
            raise
    
    def _calculate_metrics(self, contents: List[ParsedContent], result: ProcessingResult) -> None:
        """Calculate quality metrics and statistics."""
        logging.info("Calculating quality metrics and statistics")

        try:
            # Create metadata generator
            generator = MetadataGenerator()

            # Calculate comprehensive metrics
            generator.calculate_metrics(contents, result, self.config)

            # Update result with file statistics
            result.files_with_snippets = sum(1 for content in contents if content.snippets)
            result.total_snippets = sum(len(content.snippets) for content in contents)

            # Log calculated metrics
            logging.info(f"Quality metrics calculated:")
            logging.info(f"  - Trust score: {result.trust_score:.1f}/10")
            logging.info(f"  - Completeness score: {result.completeness_score:.1f}/10")
            logging.info(f"  - Code coverage: {result.code_coverage_percentage:.1f}%")
            logging.info(f"  - Files with snippets: {result.files_with_snippets}/{result.total_files_scanned}")

        except Exception as e:
            error_msg = f"Failed to calculate metrics: {e}"
            logging.error(error_msg)
            result.errors.append(error_msg)
