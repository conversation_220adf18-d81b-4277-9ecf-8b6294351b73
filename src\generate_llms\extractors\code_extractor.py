"""
Code snippet extraction functionality.
"""

import logging
import re
from typing import List, Optional, Dict, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass

from ..core.models import CodeSnippet, ParsedContent, ProcessingConfig


@dataclass
class ExtractionResult:
    """Result of code extraction operation."""
    
    snippets: List[CodeSnippet]
    total_extracted: int
    languages_detected: Dict[str, int]
    errors: List[str]


class CodeExtractor(ABC):
    """Abstract base class for code extraction."""
    
    @abstractmethod
    def extract(self, content: ParsedContent, config: ProcessingConfig) -> ExtractionResult:
        """
        Extract code snippets from parsed content.
        
        Args:
            content: Parsed content to extract from
            config: Processing configuration
            
        Returns:
            ExtractionResult with extracted snippets
        """
        pass


class RegexCodeExtractor(CodeExtractor):
    """
    Regex-based code extractor for fenced code blocks and inline code.
    """
    
    def __init__(self):
        """Initialize the extractor."""
        self.logger = logging.getLogger(__name__)
        
        # Enhanced regex patterns for different code block types
        self.fenced_block_pattern = re.compile(
            r'```(\w+)?\s*\n(.*?)\n```',
            re.DOTALL | re.MULTILINE
        )

        # Alternative fenced block patterns (tildes, different lengths)
        self.alt_fenced_pattern = re.compile(
            r'~~~(\w+)?\s*\n(.*?)\n~~~',
            re.DOTALL | re.MULTILINE
        )

        # HTML code patterns with various attributes
        self.html_code_pattern = re.compile(
            r'<(?:pre|code)(?:\s+(?:class="(?:language-)?(\w+)"|data-lang="(\w+)"))?[^>]*>(.*?)</(?:pre|code)>',
            re.DOTALL | re.IGNORECASE
        )

        # Script tags (JavaScript)
        self.script_pattern = re.compile(
            r'<script(?:\s+[^>]*)?>(?:\s*//.*?\n)?(.*?)</script>',
            re.DOTALL | re.IGNORECASE
        )

        # Style tags (CSS)
        self.style_pattern = re.compile(
            r'<style(?:\s+[^>]*)?>(?:\s*/\*.*?\*/\s*)?(.*?)</style>',
            re.DOTALL | re.IGNORECASE
        )

        # Inline code (more restrictive to avoid false positives)
        self.inline_code_pattern = re.compile(
            r'`([^`\n]{1,100})`'
        )

        # RST code blocks
        self.rst_code_pattern = re.compile(
            r'(?:^|\n)\s*\.\.\s+code(?:-block)?::\s*(\w+)?\s*\n((?:\n|\s{4,}.*)*)',
            re.MULTILINE
        )

        # RST literal blocks
        self.rst_literal_pattern = re.compile(
            r'(?:^|\n)(.*)::\s*\n((?:\n|\s{4,}.*)*)',
            re.MULTILINE
        )

        # AsciiDoc source blocks
        self.asciidoc_source_pattern = re.compile(
            r'\[source,(\w+)\]\s*\n-{4,}\s*\n(.*?)\n-{4,}',
            re.DOTALL | re.MULTILINE
        )

        # AsciiDoc listing blocks
        self.asciidoc_listing_pattern = re.compile(
            r'\.{4,}\s*\n(.*?)\n\.{4,}',
            re.DOTALL | re.MULTILINE
        )
        
        # Enhanced language detection heuristics
        self.language_heuristics = {
            'python': [
                r'def\s+\w+\(', r'import\s+\w+', r'from\s+\w+\s+import', r'print\(',
                r'if\s+__name__\s*==\s*["\']__main__["\']', r'class\s+\w+\(', r'self\.',
                r'@\w+', r'lambda\s+', r'yield\s+', r'with\s+\w+', r'except\s+\w*:',
                r'elif\s+', r'raise\s+\w+', r'assert\s+', r'pass\s*$'
            ],
            'javascript': [
                r'function\s+\w+\(', r'const\s+\w+\s*=', r'console\.log\(', r'=>',
                r'var\s+\w+\s*=', r'let\s+\w+\s*=', r'document\.', r'window\.',
                r'addEventListener\(', r'querySelector\(', r'async\s+function',
                r'await\s+', r'Promise\.', r'JSON\.', r'Array\.', r'Object\.'
            ],
            'typescript': [
                r'interface\s+\w+', r'type\s+\w+\s*=', r':\s*\w+\s*=', r'<\w+>',
                r'export\s+interface', r'export\s+type', r'implements\s+\w+',
                r'extends\s+\w+', r'public\s+\w+', r'private\s+\w+', r'readonly\s+'
            ],
            'java': [
                r'public\s+class\s+\w+', r'public\s+static\s+void\s+main', r'System\.out\.println',
                r'import\s+java\.', r'@Override', r'extends\s+\w+', r'implements\s+\w+',
                r'private\s+\w+', r'protected\s+\w+', r'final\s+\w+', r'static\s+\w+'
            ],
            'csharp': [
                r'using\s+System', r'namespace\s+\w+', r'public\s+class\s+\w+',
                r'Console\.WriteLine', r'static\s+void\s+Main', r'var\s+\w+\s*=',
                r'public\s+static\s+', r'private\s+\w+', r'protected\s+\w+', r'override\s+'
            ],
            'cpp': [
                r'#include\s*<', r'using\s+namespace', r'int\s+main\(', r'std::',
                r'cout\s*<<', r'cin\s*>>', r'class\s+\w+', r'public:', r'private:',
                r'virtual\s+', r'const\s+\w+', r'template\s*<'
            ],
            'c': [
                r'#include\s*<', r'int\s+main\(', r'printf\(', r'scanf\(',
                r'malloc\(', r'free\(', r'struct\s+\w+', r'typedef\s+',
                r'static\s+\w+', r'extern\s+\w+'
            ],
            'go': [
                r'package\s+\w+', r'import\s+\(', r'func\s+\w+\(', r'fmt\.Print',
                r'var\s+\w+\s+\w+', r'type\s+\w+\s+struct', r'interface\s*{',
                r'go\s+\w+\(', r'defer\s+', r'chan\s+\w+'
            ],
            'rust': [
                r'fn\s+\w+\(', r'let\s+\w+\s*=', r'use\s+\w+', r'struct\s+\w+',
                r'impl\s+\w+', r'trait\s+\w+', r'enum\s+\w+', r'match\s+\w+',
                r'println!\(', r'vec!\[', r'&mut\s+', r'pub\s+fn'
            ],
            'sql': [
                r'SELECT\s+', r'FROM\s+\w+', r'WHERE\s+', r'INSERT\s+INTO',
                r'UPDATE\s+\w+', r'DELETE\s+FROM', r'CREATE\s+TABLE', r'ALTER\s+TABLE',
                r'JOIN\s+\w+', r'GROUP\s+BY', r'ORDER\s+BY', r'HAVING\s+'
            ],
            'bash': [
                r'#!/bin/bash', r'\$\w+', r'echo\s+', r'if\s*\[', r'for\s+\w+\s+in',
                r'while\s*\[', r'function\s+\w+', r'export\s+\w+', r'source\s+',
                r'chmod\s+', r'grep\s+', r'awk\s+', r'sed\s+'
            ],
            'powershell': [
                r'\$\w+\s*=', r'Get-\w+', r'Set-\w+', r'New-\w+', r'Remove-\w+',
                r'Write-Host', r'Write-Output', r'param\(', r'function\s+\w+',
                r'if\s*\(', r'foreach\s*\(', r'while\s*\('
            ],
            'html': [
                r'<!DOCTYPE\s+html>', r'<html>', r'<head>', r'<body>', r'<div',
                r'<p>', r'<script>', r'<style>', r'<link\s+', r'<meta\s+',
                r'class\s*=', r'id\s*=', r'href\s*='
            ],
            'css': [
                r'\.\w+\s*{', r'#\w+\s*{', r':\s*\w+;', r'@media\s+',
                r'@import\s+', r'@keyframes\s+', r'hover\s*:', r'focus\s*:',
                r'margin\s*:', r'padding\s*:', r'color\s*:', r'background\s*:'
            ],
            'json': [
                r'^\s*{', r'^\s*\[', r'"\w+"\s*:', r':\s*"', r':\s*\d+',
                r':\s*true', r':\s*false', r':\s*null'
            ],
            'yaml': [
                r'^\w+:', r'^\s+-\s+', r'---\s*$', r'^\s*#', r':\s*\|',
                r':\s*>', r'version\s*:', r'name\s*:', r'description\s*:'
            ],
            'xml': [
                r'<\?xml\s+', r'<\w+>', r'</\w+>', r'xmlns\s*=', r'<!\[CDATA\[',
                r'<!--', r'-->', r'<\w+\s+\w+\s*='
            ],
            'dockerfile': [
                r'^FROM\s+', r'^RUN\s+', r'^COPY\s+', r'^ADD\s+', r'^WORKDIR\s+',
                r'^EXPOSE\s+', r'^ENV\s+', r'^CMD\s+', r'^ENTRYPOINT\s+'
            ],
            'makefile': [
                r'^\w+:', r'\$\(\w+\)', r'\.PHONY\s*:', r'include\s+',
                r'ifdef\s+', r'ifndef\s+', r'endif\s*$', r'@echo\s+'
            ]
        }
    
    def extract(self, content: ParsedContent, config: ProcessingConfig) -> ExtractionResult:
        """
        Extract code snippets using regex patterns.
        
        Args:
            content: Parsed content with raw text
            config: Processing configuration with language filters
            
        Returns:
            ExtractionResult with extracted code snippets
        """
        self.logger.debug(f"Extracting code from {content.file_path}")
        
        snippets = []
        languages_detected = {}
        errors = []
        
        if content.error or not content.raw_content:
            return ExtractionResult(
                snippets=[],
                total_extracted=0,
                languages_detected={},
                errors=[content.error] if content.error else ["No content to extract from"]
            )
        
        try:
            # Extract fenced code blocks (standard and alternative)
            fenced_snippets = self._extract_fenced_blocks(content.raw_content, content.file_path)
            snippets.extend(fenced_snippets)

            alt_fenced_snippets = self._extract_alt_fenced_blocks(content.raw_content, content.file_path)
            snippets.extend(alt_fenced_snippets)

            # Extract HTML code blocks
            html_snippets = self._extract_html_blocks(content.raw_content, content.file_path)
            snippets.extend(html_snippets)

            # Extract script and style tags
            script_snippets = self._extract_script_blocks(content.raw_content, content.file_path)
            snippets.extend(script_snippets)

            style_snippets = self._extract_style_blocks(content.raw_content, content.file_path)
            snippets.extend(style_snippets)

            # Extract format-specific blocks based on file type
            if content.file_type == "rst":
                rst_snippets = self._extract_rst_blocks(content.raw_content, content.file_path)
                snippets.extend(rst_snippets)
            elif content.file_type == "asciidoc":
                asciidoc_snippets = self._extract_asciidoc_blocks(content.raw_content, content.file_path)
                snippets.extend(asciidoc_snippets)

            # Extract inline code if configured
            if config.mode == "full_context":
                inline_snippets = self._extract_inline_code(content.raw_content, content.file_path)
                snippets.extend(inline_snippets)
            
            # Filter by language if specified
            if config.languages:
                snippets = self._filter_by_language(snippets, config.languages)

            # Apply content-based filtering to exclude non-meaningful code (if enabled)
            if hasattr(config, 'enable_code_filtering') and config.enable_code_filtering:
                try:
                    snippets = self._filter_meaningful_code(snippets, config)
                except Exception as e:
                    self.logger.warning(f"Code filtering failed, continuing without filtering: {e}")
                    # Continue without filtering if there's an error

            # Count languages
            for snippet in snippets:
                lang = snippet.language or "unknown"
                languages_detected[lang] = languages_detected.get(lang, 0) + 1
            
        except Exception as e:
            error_msg = f"Extraction error for {content.file_path}: {e}"
            self.logger.error(error_msg)
            errors.append(error_msg)
        
        result = ExtractionResult(
            snippets=snippets,
            total_extracted=len(snippets),
            languages_detected=languages_detected,
            errors=errors
        )
        
        self.logger.debug(f"Extracted {len(snippets)} snippets from {content.file_path}")
        return result
    
    def _extract_fenced_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract fenced code blocks (```language)."""
        snippets = []
        
        for match in self.fenced_block_pattern.finditer(text):
            language = match.group(1)
            code_content = match.group(2).strip()
            
            if not code_content:
                continue
            
            # Detect language if not specified
            if not language:
                language = self._detect_language(code_content)
            
            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )
            
            snippets.append(snippet)
        
        return snippets
    
    def _extract_html_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract HTML code blocks (<pre><code>)."""
        snippets = []
        
        for match in self.html_code_pattern.finditer(text):
            # Handle multiple capture groups for language detection
            language = match.group(1) or match.group(2)  # class="language-X" or data-lang="X"
            code_content = match.group(3).strip()
            
            if not code_content:
                continue
            
            # Clean HTML entities and tags
            code_content = self._clean_html_content(code_content)
            
            # Detect language if not specified
            if not language:
                language = self._detect_language(code_content)
            
            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )
            
            snippets.append(snippet)
        
        return snippets
    
    def _extract_inline_code(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract inline code (`code`)."""
        snippets = []
        
        for match in self.inline_code_pattern.finditer(text):
            code_content = match.group(1).strip()
            
            if not code_content or len(code_content) < 3:  # Skip very short inline code
                continue
            
            snippet = CodeSnippet(
                content=code_content,
                language="inline",
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )
            
            snippets.append(snippet)
        
        return snippets
    
    def _detect_language(self, code: str) -> Optional[str]:
        """
        Detect programming language using enhanced heuristics.

        Args:
            code: Code content to analyze

        Returns:
            Detected language name or None
        """
        if not code.strip():
            return None

        # Score each language based on pattern matches
        scores = {}
        code_lines = code.split('\n')

        for language, patterns in self.language_heuristics.items():
            score = 0
            pattern_matches = 0

            for pattern in patterns:
                # Check entire code block
                if re.search(pattern, code, re.IGNORECASE | re.MULTILINE):
                    pattern_matches += 1

                    # Give extra weight to patterns found in first few lines
                    for i, line in enumerate(code_lines[:5]):
                        if re.search(pattern, line, re.IGNORECASE):
                            score += 2  # Higher weight for early matches
                            break
                    else:
                        score += 1  # Standard weight for other matches

            # Bonus for multiple pattern matches (indicates stronger confidence)
            if pattern_matches > 1:
                score += pattern_matches * 0.5

            if score > 0:
                scores[language] = score

        # Return language with highest score, but only if confident enough
        if scores:
            best_language = max(scores, key=scores.get)
            best_score = scores[best_language]

            # Require minimum confidence threshold
            if best_score >= 2.0:
                return best_language

        # Fallback: try to detect by common file extensions in comments
        extension_hints = {
            '.py': 'python', '.js': 'javascript', '.ts': 'typescript',
            '.java': 'java', '.cs': 'csharp', '.cpp': 'cpp', '.c': 'c',
            '.go': 'go', '.rs': 'rust', '.sql': 'sql', '.sh': 'bash',
            '.ps1': 'powershell', '.html': 'html', '.css': 'css',
            '.json': 'json', '.yml': 'yaml', '.yaml': 'yaml', '.xml': 'xml'
        }

        for ext, lang in extension_hints.items():
            if ext in code.lower():
                return lang

        return None
    
    def _clean_html_content(self, content: str) -> str:
        """Clean HTML entities and tags from code content."""
        # Basic HTML entity decoding
        content = content.replace('&lt;', '<')
        content = content.replace('&gt;', '>')
        content = content.replace('&amp;', '&')
        content = content.replace('&quot;', '"')
        content = content.replace('&#39;', "'")
        
        # Remove HTML tags
        content = re.sub(r'<[^>]+>', '', content)
        
        return content
    
    def _filter_by_language(self, snippets: List[CodeSnippet], allowed_languages: List[str]) -> List[CodeSnippet]:
        """Filter snippets by allowed languages."""
        allowed_set = set(lang.lower() for lang in allowed_languages)
        
        filtered = []
        for snippet in snippets:
            if snippet.language and snippet.language.lower() in allowed_set:
                filtered.append(snippet)
            elif not snippet.language and "unknown" in allowed_set:
                filtered.append(snippet)
        
        return filtered

    def _filter_meaningful_code(self, snippets: List[CodeSnippet], config: ProcessingConfig) -> List[CodeSnippet]:
        """
        Filter code snippets to include only meaningful code elements.

        Excludes:
        - Pure HTML markup without significant logic
        - Pure CSS styling without dynamic content
        - Presentational code without business logic

        Includes:
        - API function calls and method invocations
        - Business logic functions and methods
        - Data processing operations
        - Control flow statements (if/else, loops, etc.)
        - Variable declarations with business significance
        """
        # Check if filtering is disabled via configuration
        if hasattr(config, 'enable_code_filtering') and not config.enable_code_filtering:
            return snippets

        filtered = []
        for snippet in snippets:
            if self._is_meaningful_code(snippet):
                filtered.append(snippet)
            else:
                self.logger.debug(f"Filtered out non-meaningful code snippet: {snippet.language} from {snippet.file_path}")

        return filtered

    def _is_meaningful_code(self, snippet: CodeSnippet) -> bool:
        """
        Determine if a code snippet contains meaningful code elements.

        Args:
            snippet: Code snippet to analyze

        Returns:
            True if the snippet contains meaningful code, False otherwise
        """
        if not snippet.content or not snippet.content.strip():
            return False

        content = snippet.content.strip()
        language = (snippet.language or "").lower()

        # Always include certain languages that are typically meaningful
        if language in ['python', 'java', 'javascript', 'typescript', 'go', 'rust', 'c', 'cpp', 'c++', 'csharp', 'php', 'ruby', 'kotlin', 'swift', 'scala', 'r', 'matlab', 'sql']:
            return self._has_meaningful_patterns(content, language)

        # Handle HTML - only include if it has significant logic
        if language in ['html', 'xml']:
            return self._is_meaningful_html(content)

        # Handle CSS - only include if it has dynamic/programmatic content
        if language in ['css', 'scss', 'sass', 'less']:
            return self._is_meaningful_css(content)

        # Handle shell scripts and configuration
        if language in ['bash', 'shell', 'sh', 'zsh', 'fish', 'powershell', 'batch', 'cmd']:
            return self._is_meaningful_script(content)

        # Handle configuration files
        if language in ['json', 'yaml', 'yml', 'toml', 'ini', 'xml', 'config']:
            return self._is_meaningful_config(content)

        # For unknown or other languages, apply general meaningful patterns
        return self._has_meaningful_patterns(content, language)

    def _has_meaningful_patterns(self, content: str, language: str) -> bool:
        """Check if content has meaningful programming patterns."""
        # Patterns that indicate meaningful code
        meaningful_patterns = [
            # Function calls and method invocations
            r'\w+\s*\([^)]*\)',  # function_name(args)
            r'\w+\.\w+\s*\([^)]*\)',  # object.method(args)

            # Control flow statements
            r'\b(if|else|elif|while|for|switch|case|try|catch|finally|with)\b',

            # Variable assignments with meaningful operations
            r'\w+\s*[=]\s*[^=]',  # variable = value (not comparison)

            # Class and function definitions
            r'\b(class|def|function|func|fn|proc|sub|method)\s+\w+',

            # Import/include statements
            r'\b(import|from|include|require|use|using|#include)\b',

            # API calls and HTTP methods
            r'\b(GET|POST|PUT|DELETE|PATCH|fetch|axios|request|http)\b',

            # Database operations
            r'\b(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)\b',

            # Loops and iterations
            r'\b(forEach|map|filter|reduce|each|iterate)\b',

            # Error handling
            r'\b(throw|raise|error|exception|catch|rescue)\b',

            # Async operations
            r'\b(async|await|promise|then|catch|callback)\b',
        ]

        # Check for meaningful patterns
        for pattern in meaningful_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True

        # Additional checks for specific languages
        if language in ['python']:
            python_patterns = [
                r'\bdef\s+\w+',  # function definitions
                r'\bclass\s+\w+',  # class definitions
                r'\bif\s+__name__\s*==\s*["\']__main__["\']',  # main guard
                r'@\w+',  # decorators
            ]
            for pattern in python_patterns:
                if re.search(pattern, content):
                    return True

        elif language in ['javascript', 'typescript']:
            js_patterns = [
                r'\bfunction\s+\w+',  # function declarations
                r'\w+\s*=>\s*',  # arrow functions
                r'\bconst\s+\w+\s*=',  # const declarations
                r'\blet\s+\w+\s*=',  # let declarations
                r'\bvar\s+\w+\s*=',  # var declarations
            ]
            for pattern in js_patterns:
                if re.search(pattern, content):
                    return True

        # If content is very short and doesn't match patterns, likely not meaningful
        if len(content.strip()) < 20:
            return False

        # If content has multiple lines with some complexity, likely meaningful
        lines = content.strip().split('\n')
        if len(lines) > 3 and any(len(line.strip()) > 10 for line in lines):
            return True

        return False

    def _is_meaningful_html(self, content: str) -> bool:
        """Check if HTML content has meaningful logic beyond pure markup."""
        # Look for dynamic content, templating, or interactive elements
        meaningful_html_patterns = [
            # Templating and dynamic content
            r'\{\{.*?\}\}',  # Handlebars, Angular, etc.
            r'\{%.*?%\}',  # Django, Jinja2
            r'<%.*?%>',  # JSP, ASP
            r'\$\{.*?\}',  # JSP EL

            # Framework-specific attributes
            r'\b(ng-|v-|@|:|\*ng)',  # Angular, Vue directives
            r'\b(data-|aria-)',  # Data and accessibility attributes

            # Interactive elements
            r'\b(onclick|onload|onchange|onsubmit|addEventListener)',

            # Form elements with logic
            r'<(form|input|select|textarea)[^>]*>',

            # Script references
            r'<script[^>]*src=',
        ]

        for pattern in meaningful_html_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True

        # Pure markup without logic is not meaningful
        return False

    def _is_meaningful_css(self, content: str) -> bool:
        """Check if CSS content has meaningful dynamic or programmatic content."""
        # Look for dynamic CSS, variables, or complex logic
        meaningful_css_patterns = [
            # CSS variables and custom properties
            r'--[\w-]+\s*:',  # CSS custom properties
            r'var\(--[\w-]+\)',  # CSS variable usage

            # Preprocessor features
            r'@(mixin|include|extend|function|if|for|each|while)',  # SCSS/Sass
            r'\$[\w-]+\s*:',  # SCSS variables
            r'#\{.*?\}',  # SCSS interpolation

            # CSS-in-JS or dynamic styles
            r'\$\{.*?\}',  # Template literals

            # Complex selectors that indicate logic
            r':nth-child\(',
            r':not\(',
            r'\[.*?=.*?\]',  # Attribute selectors

            # Media queries and responsive design
            r'@media\s*\(',
            r'@supports\s*\(',

            # Animations and transitions
            r'@keyframes',
            r'animation\s*:',
            r'transition\s*:',
        ]

        for pattern in meaningful_css_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True

        # Pure styling without logic is not meaningful
        return False

    def _is_meaningful_script(self, content: str) -> bool:
        """Check if shell script content has meaningful operations."""
        # Look for actual commands and logic, not just comments
        meaningful_script_patterns = [
            # Variable operations
            r'\$\w+',  # Variable usage
            r'\w+=',  # Variable assignment

            # Control flow
            r'\b(if|then|else|elif|fi|for|while|do|done|case|esac)\b',

            # Function definitions
            r'\w+\(\)\s*\{',
            r'function\s+\w+',

            # Command execution
            r'`[^`]+`',  # Command substitution
            r'\$\([^)]+\)',  # Command substitution

            # File operations
            r'\b(cp|mv|rm|mkdir|chmod|chown|find|grep|sed|awk)\b',

            # Network operations
            r'\b(curl|wget|ssh|scp|rsync)\b',

            # Package management
            r'\b(apt|yum|brew|pip|npm|yarn)\b',
        ]

        for pattern in meaningful_script_patterns:
            if re.search(pattern, content):
                return True

        return False

    def _is_meaningful_config(self, content: str) -> bool:
        """Check if configuration content has meaningful settings."""
        # Configuration files are generally meaningful if they're not empty
        # and contain actual configuration data

        # Skip if mostly comments
        lines = content.strip().split('\n')
        non_comment_lines = []

        for line in lines:
            stripped = line.strip()
            if stripped and not stripped.startswith('#') and not stripped.startswith('//'):
                non_comment_lines.append(stripped)

        # Must have some actual configuration content
        if len(non_comment_lines) < 2:
            return False

        # Look for configuration patterns
        config_patterns = [
            r'\w+\s*[=:]\s*\w+',  # key = value or key: value
            r'[\"\'][\w\s]+[\"\']',  # quoted strings
            r'\{.*?\}',  # objects
            r'\[.*?\]',  # arrays
        ]

        for pattern in config_patterns:
            if re.search(pattern, content):
                return True

        return len(non_comment_lines) > 0

    def _extract_alt_fenced_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract alternative fenced code blocks (~~~language)."""
        snippets = []

        for match in self.alt_fenced_pattern.finditer(text):
            language = match.group(1)
            code_content = match.group(2).strip()

            if not code_content:
                continue

            # Detect language if not specified
            if not language:
                language = self._detect_language(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets

    def _extract_script_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract JavaScript from <script> tags."""
        snippets = []

        for match in self.script_pattern.finditer(text):
            code_content = match.group(1).strip()

            if not code_content:
                continue

            # Clean HTML entities
            code_content = self._clean_html_content(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language="javascript",
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets

    def _extract_style_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract CSS from <style> tags."""
        snippets = []

        for match in self.style_pattern.finditer(text):
            code_content = match.group(1).strip()

            if not code_content:
                continue

            # Clean HTML entities
            code_content = self._clean_html_content(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language="css",
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets

    def _extract_rst_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract RST code blocks and literal blocks."""
        snippets = []

        # Extract code-block:: and code:: directives
        for match in self.rst_code_pattern.finditer(text):
            language = match.group(1)
            code_content = match.group(2)

            if not code_content:
                continue

            # Clean indentation (RST uses 4+ spaces for code blocks)
            lines = code_content.split('\n')
            cleaned_lines = []
            for line in lines:
                if line.strip():  # Non-empty line
                    if line.startswith('    '):
                        cleaned_lines.append(line[4:])  # Remove 4-space indent
                    else:
                        cleaned_lines.append(line.lstrip())
                else:
                    cleaned_lines.append('')

            code_content = '\n'.join(cleaned_lines).strip()

            if not code_content:
                continue

            # Detect language if not specified
            if not language:
                language = self._detect_language(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        # Extract literal blocks (::)
        for match in self.rst_literal_pattern.finditer(text):
            intro_text = match.group(1).strip()
            code_content = match.group(2)

            if not code_content or 'code' not in intro_text.lower():
                continue

            # Clean indentation
            lines = code_content.split('\n')
            cleaned_lines = []
            for line in lines:
                if line.strip():
                    if line.startswith('    '):
                        cleaned_lines.append(line[4:])
                    else:
                        cleaned_lines.append(line.lstrip())
                else:
                    cleaned_lines.append('')

            code_content = '\n'.join(cleaned_lines).strip()

            if not code_content:
                continue

            language = self._detect_language(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets

    def _extract_asciidoc_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract AsciiDoc source blocks and listing blocks."""
        snippets = []

        # Extract [source,language] blocks
        for match in self.asciidoc_source_pattern.finditer(text):
            language = match.group(1)
            code_content = match.group(2).strip()

            if not code_content:
                continue

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        # Extract listing blocks (.... blocks)
        for match in self.asciidoc_listing_pattern.finditer(text):
            code_content = match.group(1).strip()

            if not code_content:
                continue

            language = self._detect_language(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets
