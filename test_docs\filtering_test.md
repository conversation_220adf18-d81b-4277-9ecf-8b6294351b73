# Filtering Test Document

This document contains various types of code to test the filtering functionality.

## Meaningful Python Code

```python
def calculate_total(items):
    """Calculate total price of items."""
    total = 0
    for item in items:
        if item.price > 0:
            total += item.price
    return total

# API call example
response = requests.get('/api/users')
users = response.json()
```

## Pure HTML Markup (should be filtered)

```html
<div class="container">
    <h1>Welcome</h1>
    <p>This is just static content.</p>
    <div class="footer">
        <span>Copyright 2024</span>
    </div>
</div>
```

## Pure CSS Styling (should be filtered)

```css
.container {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background-color: #f0f0f0;
    border-bottom: 1px solid #ccc;
}
```

## Meaningful JavaScript

```javascript
function fetchUserData(userId) {
    return fetch(`/api/users/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            return data;
        });
}

// Event handling
document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('submit');
    button.onclick = handleSubmit;
});
```

## HTML with Logic (should be kept)

```html
<form onsubmit="handleSubmit(event)">
    <input type="text" ng-model="user.name" required>
    <button type="submit" :disabled="!isValid">Submit</button>
    <div *ngIf="showError" class="error">{{ errorMessage }}</div>
</form>
```

## CSS with Dynamic Content (should be kept)

```css
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
}

@media (max-width: 768px) {
    .container {
        padding: var(--primary-color);
        display: none;
    }
}

.dynamic-content {
    color: var(--primary-color);
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
```
