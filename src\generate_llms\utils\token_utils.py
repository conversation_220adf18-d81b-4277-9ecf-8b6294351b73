"""
Token counting and text chunking utilities.
"""

import logging
import re
from typing import List, Optional, Tuple
from dataclasses import dataclass

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False


@dataclass
class TextChunk:
    """Represents a chunk of text with metadata."""
    
    content: str
    start_token: int
    end_token: int
    chunk_index: int
    total_chunks: int
    overlap_with_previous: bool = False
    overlap_with_next: bool = False


class TokenCounter:
    """Handles token counting using tiktoken or fallback methods."""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        """
        Initialize token counter.
        
        Args:
            model_name: Model name for tiktoken encoding (e.g., 'gpt-3.5-turbo', 'gpt-4')
        """
        self.logger = logging.getLogger(__name__)
        self.model_name = model_name
        self.encoding = None
        
        if TIKTOKEN_AVAILABLE:
            try:
                # Try to get encoding for the specific model
                self.encoding = tiktoken.encoding_for_model(model_name)
                self.logger.debug(f"Using tiktoken encoding for model: {model_name}")
            except KeyError:
                # Fallback to a common encoding
                try:
                    self.encoding = tiktoken.get_encoding("cl100k_base")  # GPT-4 encoding
                    self.logger.debug(f"Model {model_name} not found, using cl100k_base encoding")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize tiktoken: {e}")
                    self.encoding = None
        
        if not self.encoding:
            self.logger.info("Using fallback token counting (4 chars per token)")
    
    def count_tokens(self, text: str) -> int:
        """
        Count tokens in text.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        if not text:
            return 0
        
        if self.encoding:
            try:
                return len(self.encoding.encode(text))
            except Exception as e:
                self.logger.warning(f"tiktoken encoding failed: {e}, using fallback")
        
        # Fallback: estimate 4 characters per token
        return max(1, len(text) // 4)
    
    def encode_text(self, text: str) -> List[int]:
        """
        Encode text to tokens.
        
        Args:
            text: Text to encode
            
        Returns:
            List of token IDs
        """
        if self.encoding:
            try:
                return self.encoding.encode(text)
            except Exception as e:
                self.logger.warning(f"tiktoken encoding failed: {e}")
        
        # Fallback: return character-based approximation
        return list(range(len(text) // 4))
    
    def decode_tokens(self, tokens: List[int]) -> str:
        """
        Decode tokens back to text.
        
        Args:
            tokens: List of token IDs
            
        Returns:
            Decoded text
        """
        if self.encoding:
            try:
                return self.encoding.decode(tokens)
            except Exception as e:
                self.logger.warning(f"tiktoken decoding failed: {e}")
        
        # Fallback: return placeholder
        return f"[{len(tokens)} tokens]"


class TextChunker:
    """Handles intelligent text chunking for large content."""
    
    def __init__(self, token_counter: TokenCounter):
        """
        Initialize text chunker.
        
        Args:
            token_counter: TokenCounter instance for measuring text
        """
        self.token_counter = token_counter
        self.logger = logging.getLogger(__name__)
    
    def chunk_text(
        self, 
        text: str, 
        max_tokens: int = 4000, 
        overlap_tokens: int = 200
    ) -> List[TextChunk]:
        """
        Split text into chunks with intelligent boundaries.
        
        Args:
            text: Text to chunk
            max_tokens: Maximum tokens per chunk
            overlap_tokens: Tokens to overlap between chunks
            
        Returns:
            List of TextChunk objects
        """
        if not text.strip():
            return []
        
        total_tokens = self.token_counter.count_tokens(text)
        
        # If text fits in one chunk, return as-is
        if total_tokens <= max_tokens:
            return [TextChunk(
                content=text,
                start_token=0,
                end_token=total_tokens,
                chunk_index=0,
                total_chunks=1
            )]
        
        # Split text into logical boundaries
        chunks = self._split_with_boundaries(text, max_tokens, overlap_tokens)
        
        # Add metadata to chunks
        for i, chunk in enumerate(chunks):
            chunk.chunk_index = i
            chunk.total_chunks = len(chunks)
            chunk.overlap_with_previous = i > 0 and overlap_tokens > 0
            chunk.overlap_with_next = i < len(chunks) - 1 and overlap_tokens > 0
        
        self.logger.info(f"Split text into {len(chunks)} chunks (total: {total_tokens} tokens)")
        return chunks
    
    def _split_with_boundaries(
        self, 
        text: str, 
        max_tokens: int, 
        overlap_tokens: int
    ) -> List[TextChunk]:
        """Split text respecting logical boundaries."""
        chunks = []
        
        # Try to split by paragraphs first
        paragraphs = re.split(r'\n\s*\n', text)
        
        current_chunk = ""
        current_start_token = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # Check if adding this paragraph would exceed the limit
            test_content = current_chunk + "\n\n" + paragraph if current_chunk else paragraph
            test_tokens = self.token_counter.count_tokens(test_content)
            
            if test_tokens <= max_tokens:
                # Add paragraph to current chunk
                current_chunk = test_content
            else:
                # Save current chunk if it has content
                if current_chunk:
                    chunk_tokens = self.token_counter.count_tokens(current_chunk)
                    chunks.append(TextChunk(
                        content=current_chunk,
                        start_token=current_start_token,
                        end_token=current_start_token + chunk_tokens,
                        chunk_index=0,  # Will be set later
                        total_chunks=0   # Will be set later
                    ))
                    current_start_token += chunk_tokens - overlap_tokens
                
                # Start new chunk with current paragraph
                current_chunk = paragraph
                
                # If single paragraph is too large, split it further
                if self.token_counter.count_tokens(paragraph) > max_tokens:
                    para_chunks = self._split_paragraph(
                        paragraph, max_tokens, overlap_tokens, current_start_token
                    )
                    chunks.extend(para_chunks)
                    current_chunk = ""
                    if para_chunks:
                        current_start_token = para_chunks[-1].end_token - overlap_tokens
        
        # Add final chunk
        if current_chunk:
            chunk_tokens = self.token_counter.count_tokens(current_chunk)
            chunks.append(TextChunk(
                content=current_chunk,
                start_token=current_start_token,
                end_token=current_start_token + chunk_tokens,
                chunk_index=0,  # Will be set later
                total_chunks=0   # Will be set later
            ))
        
        return chunks
    
    def _split_paragraph(
        self, 
        paragraph: str, 
        max_tokens: int, 
        overlap_tokens: int,
        start_token: int
    ) -> List[TextChunk]:
        """Split a large paragraph by sentences."""
        chunks = []
        
        # Split by sentences
        sentences = re.split(r'(?<=[.!?])\s+', paragraph)
        
        current_chunk = ""
        current_start = start_token
        
        for sentence in sentences:
            test_content = current_chunk + " " + sentence if current_chunk else sentence
            test_tokens = self.token_counter.count_tokens(test_content)
            
            if test_tokens <= max_tokens:
                current_chunk = test_content
            else:
                # Save current chunk
                if current_chunk:
                    chunk_tokens = self.token_counter.count_tokens(current_chunk)
                    chunks.append(TextChunk(
                        content=current_chunk,
                        start_token=current_start,
                        end_token=current_start + chunk_tokens,
                        chunk_index=0,
                        total_chunks=0
                    ))
                    current_start += chunk_tokens - overlap_tokens
                
                # Start new chunk
                current_chunk = sentence
        
        # Add final chunk
        if current_chunk:
            chunk_tokens = self.token_counter.count_tokens(current_chunk)
            chunks.append(TextChunk(
                content=current_chunk,
                start_token=current_start,
                end_token=current_start + chunk_tokens,
                chunk_index=0,
                total_chunks=0
            ))
        
        return chunks
