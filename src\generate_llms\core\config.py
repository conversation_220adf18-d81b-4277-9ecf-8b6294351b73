"""
Configuration management for generate-llms.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, field_validator, ValidationError

from .models import ProcessingConfig


class ConfigSchema(BaseModel):
    """Pydantic schema for configuration validation."""

    input_directory: Optional[str] = None
    output_file: Optional[str] = None
    mode: str = Field(default="full_context", pattern="^(full_context|snippets_only)$")
    max_depth: Optional[int] = Field(default=None, ge=1)
    languages: Optional[list] = None
    exclude_patterns: Optional[list] = None
    include_folders: Optional[list] = None

    # Project metadata (Context7-inspired)
    project_title: Optional[str] = None
    project_description: Optional[str] = None
    project_rules: Optional[list] = None

    # LLM settings
    llm: Optional[Dict[str, Any]] = Field(default_factory=dict)

    # Logging settings
    logging: Optional[Dict[str, Any]] = Field(default_factory=dict)

    # Performance settings
    performance: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    @field_validator('languages')
    @classmethod
    def validate_languages(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError('languages must be a list')
        return v

    @field_validator('exclude_patterns')
    @classmethod
    def validate_exclude_patterns(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError('exclude_patterns must be a list')
        return v

    @field_validator('llm')
    @classmethod
    def validate_llm_config(cls, v):
        if v is not None:
            # Validate LLM backend
            if 'backend' in v and v['backend'] not in ['none', 'ollama', 'llamacpp', 'transformers', 'lmstudio']:
                raise ValueError(f"Invalid LLM backend: {v['backend']}. Must be one of: none, ollama, llamacpp, transformers, lmstudio")

            # Validate temperature
            if 'temperature' in v:
                temp = v['temperature']
                if not isinstance(temp, (int, float)) or not (0.1 <= temp <= 1.0):
                    raise ValueError(f"LLM temperature must be between 0.1 and 1.0, got: {temp}")

            # Validate max_tokens
            if 'max_tokens' in v:
                tokens = v['max_tokens']
                if not isinstance(tokens, int) or not (50 <= tokens <= 2000):
                    raise ValueError(f"LLM max_tokens must be between 50 and 2000, got: {tokens}")

        return v

    @field_validator('performance')
    @classmethod
    def validate_performance_config(cls, v):
        if v is not None:
            # Validate parallel processing settings
            if 'max_workers' in v:
                workers = v['max_workers']
                if not isinstance(workers, int) or workers < 1:
                    raise ValueError(f"max_workers must be a positive integer, got: {workers}")

            # Validate memory limits
            if 'memory_limit_mb' in v:
                limit = v['memory_limit_mb']
                if not isinstance(limit, int) or limit < 100:
                    raise ValueError(f"memory_limit_mb must be at least 100, got: {limit}")

        return v


def load_config(config_file: Path) -> Dict[str, Any]:
    """
    Load and validate configuration from YAML file.

    Args:
        config_file: Path to YAML configuration file

    Returns:
        Validated configuration dictionary

    Raises:
        FileNotFoundError: If config file doesn't exist
        yaml.YAMLError: If YAML is invalid
        ValueError: If configuration is invalid
    """
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_file}")

    if not config_file.is_file():
        raise ValueError(f"Configuration path is not a file: {config_file}")

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            raw_config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Invalid YAML syntax in {config_file}: {e}")
    except UnicodeDecodeError as e:
        raise ValueError(f"Cannot read configuration file {config_file}: {e}")
    except PermissionError:
        raise PermissionError(f"Permission denied reading configuration file: {config_file}")

    if raw_config is None:
        raw_config = {}

    if not isinstance(raw_config, dict):
        raise ValueError(f"Configuration file must contain a YAML object/dictionary, got: {type(raw_config).__name__}")

    # Validate configuration with detailed error messages
    try:
        config_schema = ConfigSchema(**raw_config)
        return config_schema.model_dump(exclude_none=True)
    except ValidationError as e:
        # Format validation errors nicely
        error_messages = []
        for error in e.errors():
            field = '.'.join(str(loc) for loc in error['loc'])
            message = error['msg']
            error_messages.append(f"  - {field}: {message}")

        raise ValueError(f"Configuration validation failed:\n" + '\n'.join(error_messages))
    except Exception as e:
        raise ValueError(f"Unexpected error validating configuration: {e}")


def merge_config_with_cli(
    config_dict: Dict[str, Any],
    cli_config: ProcessingConfig,
    cli_defaults: Optional[ProcessingConfig] = None
) -> ProcessingConfig:
    """
    Merge configuration file with CLI arguments.
    CLI arguments take precedence over config file.

    Args:
        config_dict: Configuration from file
        cli_config: Configuration from CLI arguments
        cli_defaults: Default CLI configuration for comparison

    Returns:
        Merged ProcessingConfig
    """
    # Create a copy to avoid modifying the original
    merged_config = ProcessingConfig(
        input_directory=cli_config.input_directory,
        output_file=cli_config.output_file,
        mode=cli_config.mode,
        max_depth=cli_config.max_depth,
        languages=cli_config.languages,
        exclude_patterns=cli_config.exclude_patterns,
        include_folders=cli_config.include_folders,
        project_title=cli_config.project_title,
        project_description=cli_config.project_description,
        project_rules=cli_config.project_rules,
        llm_rewrite_enabled=cli_config.llm_rewrite_enabled,
        llm_backend=cli_config.llm_backend,
        llm_model=cli_config.llm_model,
        llm_temperature=cli_config.llm_temperature,
        llm_max_tokens=cli_config.llm_max_tokens,
        config_file=cli_config.config_file,
        verbose=cli_config.verbose,
        dry_run=cli_config.dry_run
    )

    # Apply config file values for unspecified CLI options
    # Note: This is a simplified approach - in a real implementation,
    # we'd track which CLI options were explicitly set vs defaults

    # For now, use None checks and known defaults to determine precedence
    if 'max_depth' in config_dict and cli_config.max_depth is None:
        merged_config.max_depth = config_dict['max_depth']

    if 'languages' in config_dict and cli_config.languages is None:
        merged_config.languages = config_dict['languages']

    if 'exclude_patterns' in config_dict and cli_config.exclude_patterns is None:
        merged_config.exclude_patterns = config_dict['exclude_patterns']

    if 'include_folders' in config_dict and cli_config.include_folders is None:
        merged_config.include_folders = config_dict['include_folders']

    # Handle project metadata
    if 'project_title' in config_dict and cli_config.project_title is None:
        merged_config.project_title = config_dict['project_title']

    if 'project_description' in config_dict and cli_config.project_description is None:
        merged_config.project_description = config_dict['project_description']

    if 'project_rules' in config_dict and cli_config.project_rules is None:
        merged_config.project_rules = config_dict['project_rules']
    
    # Handle LLM configuration
    if 'llm' in config_dict:
        llm_config = config_dict['llm']

        if 'enabled' in llm_config and not cli_config.llm_rewrite_enabled:
            merged_config.llm_rewrite_enabled = llm_config['enabled']

        # Only use config file LLM settings if CLI didn't specify them
        # (This is simplified - real implementation would track explicit CLI args)
        if 'backend' in llm_config and cli_config.llm_backend is None:
            merged_config.llm_backend = llm_config['backend']

        if 'model' in llm_config and not cli_config.llm_model:
            merged_config.llm_model = llm_config['model']

        if 'temperature' in llm_config and cli_config.llm_temperature == 0.3:  # Default value
            merged_config.llm_temperature = llm_config['temperature']

        if 'max_tokens' in llm_config and cli_config.llm_max_tokens == 500:  # Default value
            merged_config.llm_max_tokens = llm_config['max_tokens']

        if 'lmstudio_timeout' in llm_config:
            merged_config.lmstudio_timeout = llm_config['lmstudio_timeout']
    
    # Handle logging configuration
    if 'logging' in config_dict:
        logging_config = config_dict['logging']
        
        if 'level' in logging_config and not cli_config.verbose:
            merged_config.verbose = logging_config['level'].upper() in ['DEBUG', 'INFO']
    
    return merged_config


def create_sample_config(output_path: Path) -> None:
    """
    Create a sample configuration file with all available options.

    Args:
        output_path: Path where to create the sample config file
    """
    sample_config = {
        "# Project Information": None,
        "project_title": "My Documentation Project",
        "project_description": "Comprehensive documentation for my project",
        "project_rules": [
            "Follow PEP 8 for Python code",
            "Use TypeScript for JavaScript files",
            "Include comprehensive docstrings"
        ],

        "# Processing Settings": None,
        "mode": "full_context",  # or "snippets_only"
        "max_depth": 5,
        "languages": ["python", "javascript", "typescript", "sql", "bash"],
        "exclude_patterns": [
            "*.test.*",
            "*.spec.*",
            "node_modules/*",
            ".git/*",
            "__pycache__/*",
            "*.pyc",
            "temp/*",
            "build/*",
            "dist/*"
        ],
        "include_folders": [
            "src",
            "docs",
            "examples"
        ],

        "# LLM Configuration": None,
        "llm": {
            "enabled": False,
            "backend": "none",  # none, ollama, llamacpp, transformers, lmstudio
            "model": "llama2:7b",
            "temperature": 0.3,
            "max_tokens": 500
        },

        "# Logging Configuration": None,
        "logging": {
            "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
            "file": "generate-llms.log"
        },

        "# Performance Configuration": None,
        "performance": {
            "max_workers": 4,
            "memory_limit_mb": 1024,
            "batch_size": 100
        }
    }

    # Create YAML content with comments
    yaml_content = """# generate-llms Configuration File
# This file contains all available configuration options for the generate-llms tool.
# CLI arguments will override these settings.

# Project Information
project_title: "My Documentation Project"
project_description: "Comprehensive documentation for my project"
project_rules:
  - "Follow PEP 8 for Python code"
  - "Use TypeScript for JavaScript files"
  - "Include comprehensive docstrings"

# Processing Settings
mode: "full_context"  # Options: full_context, snippets_only
max_depth: 5  # Maximum directory depth to scan (null for unlimited)
languages:
  - python
  - javascript
  - typescript
  - sql
  - bash
exclude_patterns:
  - "*.test.*"
  - "*.spec.*"
  - "node_modules/*"
  - ".git/*"
  - "__pycache__/*"
  - "*.pyc"
  - "temp/*"
  - "build/*"
  - "dist/*"
include_folders:
  - src
  - docs
  - examples

# LLM Configuration
llm:
  enabled: false
  backend: "none"  # Options: none, ollama, llamacpp, transformers, lmstudio
  model: "llama2:7b"
  temperature: 0.3  # Range: 0.1-1.0
  max_tokens: 500   # Range: 50-2000

# Logging Configuration
logging:
  level: "INFO"  # Options: DEBUG, INFO, WARNING, ERROR
  file: "generate-llms.log"

# Performance Configuration
performance:
  max_workers: 4        # Number of parallel workers
  memory_limit_mb: 1024 # Memory limit in MB
  batch_size: 100       # Files to process in each batch
"""

    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
    except Exception as e:
        raise ValueError(f"Failed to create sample configuration file: {e}")


def validate_config_precedence(
    config_file_path: Optional[Path],
    cli_config: ProcessingConfig
) -> ProcessingConfig:
    """
    Validate and apply configuration precedence rules.

    Precedence order (highest to lowest):
    1. CLI arguments
    2. Configuration file
    3. Default values

    Args:
        config_file_path: Path to configuration file (optional)
        cli_config: Configuration from CLI arguments

    Returns:
        Final merged configuration

    Raises:
        Various exceptions for configuration errors
    """
    if config_file_path is None:
        # No config file, use CLI config as-is
        return cli_config

    try:
        # Load and validate config file
        config_dict = load_config(config_file_path)

        # Merge with CLI config (CLI takes precedence)
        merged_config = merge_config_with_cli(config_dict, cli_config)

        return merged_config

    except Exception as e:
        # Re-raise with more context
        raise ValueError(f"Configuration error: {e}")


def get_config_summary(config: ProcessingConfig) -> str:
    """
    Generate a human-readable summary of the current configuration.

    Args:
        config: ProcessingConfig to summarize

    Returns:
        Formatted configuration summary
    """
    summary_lines = [
        "📋 Configuration Summary:",
        f"  📁 Input: {config.input_directory}",
        f"  📝 Output: {config.output_file}",
        f"  🎯 Mode: {config.mode}",
        f"  🔍 Max depth: {config.max_depth or 'unlimited'}",
    ]

    if config.languages:
        summary_lines.append(f"  🌐 Languages: {', '.join(config.languages)}")

    if config.exclude_patterns:
        summary_lines.append(f"  🚫 Exclude patterns: {len(config.exclude_patterns)} patterns")

    if config.llm_backend:
        summary_lines.extend([
            f"  🤖 LLM Backend: {config.llm_backend}",
            f"  🎛️  Model: {config.llm_model}",
            f"  🌡️  Temperature: {config.llm_temperature}",
            f"  🔢 Max tokens: {config.llm_max_tokens}"
        ])

    summary_lines.append(f"  📊 Verbose: {config.verbose}")
    summary_lines.append(f"  🧪 Dry run: {config.dry_run}")

    return '\n'.join(summary_lines)
