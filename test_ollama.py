#!/usr/bin/env python3
"""
Simple test script to check Ollama connectivity and available models.
"""

import requests
import json
import sys
from typing import Dict, Any, List


def test_ollama_connection(base_url: str = "http://localhost:11434") -> Dict[str, Any]:
    """
    Test connection to Ollama API and retrieve available models.
    
    Args:
        base_url: Base URL for Ollama API
        
    Returns:
        Dictionary with connection status and available models
    """
    result = {
        "connected": False,
        "models": [],
        "error": None
    }
    
    try:
        # Test basic connectivity
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            result["connected"] = True
            data = response.json()
            result["models"] = [model["name"] for model in data.get("models", [])]
        else:
            result["error"] = f"HTTP {response.status_code}: {response.text}"
            
    except requests.exceptions.ConnectionError:
        result["error"] = "Connection refused - Ollama service may not be running"
    except requests.exceptions.Timeout:
        result["error"] = "Connection timeout - Ollama service may be slow or unresponsive"
    except Exception as e:
        result["error"] = f"Unexpected error: {e}"
    
    return result


def test_model_availability(model_name: str, base_url: str = "http://localhost:11434") -> Dict[str, Any]:
    """
    Test if a specific model is available for generation.
    
    Args:
        model_name: Name of the model to test
        base_url: Base URL for Ollama API
        
    Returns:
        Dictionary with model availability status
    """
    result = {
        "available": False,
        "error": None
    }
    
    try:
        # Try a simple generation request
        payload = {
            "model": model_name,
            "prompt": "Hello",
            "stream": False,
            "options": {
                "num_predict": 1  # Just generate 1 token to test
            }
        }
        
        response = requests.post(
            f"{base_url}/api/generate",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result["available"] = True
        else:
            result["error"] = f"HTTP {response.status_code}: {response.text}"
            
    except Exception as e:
        result["error"] = f"Error testing model: {e}"
    
    return result


def main():
    """Main test function."""
    print("🔍 Testing Ollama Connectivity...")
    print("=" * 50)
    
    # Test basic connection
    connection_result = test_ollama_connection()
    
    if connection_result["connected"]:
        print("✅ Ollama service is running")
        print(f"📋 Available models ({len(connection_result['models'])}):")
        
        if connection_result["models"]:
            for model in connection_result["models"]:
                print(f"   • {model}")
        else:
            print("   ⚠️  No models found - you may need to pull some models")
            print("   💡 Try: ollama pull llama2:7b")
        
        # Test specific models that the application might use
        test_models = ["llama2:7b", "qwen3:latest", "codellama:7b"]
        
        print("\n🧪 Testing specific models...")
        print("-" * 30)
        
        for model in test_models:
            if model in connection_result["models"]:
                print(f"✅ {model} - Available")
                
                # Test actual generation
                gen_result = test_model_availability(model)
                if gen_result["available"]:
                    print(f"   ✅ Generation test passed")
                else:
                    print(f"   ❌ Generation test failed: {gen_result['error']}")
            else:
                print(f"❌ {model} - Not installed")
                print(f"   💡 Install with: ollama pull {model}")
        
    else:
        print("❌ Ollama service is not accessible")
        print(f"🔍 Error: {connection_result['error']}")
        print("\n💡 Troubleshooting steps:")
        print("   1. Make sure Ollama is installed")
        print("   2. Start Ollama service: ollama serve")
        print("   3. Check if port 11434 is available")
        print("   4. Try: curl http://localhost:11434/api/tags")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed")


if __name__ == "__main__":
    main()
